/* IR3 Parameters Display Styles - Redesigned */

/* Base Section Styles */
.parameters-section {
  position: relative;
  min-height: 100vh;
  height: 100vh;
  overflow: hidden;
  background: #000;
  color: #fff;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Background Layer */
.parameters-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #000 100%);
  opacity: 0.95;
}

.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(0, 191, 255, 0.08) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 191, 255, 0.08) 1px, transparent 1px);
  background-size: 60px 60px;
  animation: grid-move 30s linear infinite;
}

@keyframes grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(60px, 60px); }
}

.floating-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(2px 2px at 20px 30px, #00bfff, transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(0, 191, 255, 0.8), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(0, 191, 255, 0.6), transparent),
    radial-gradient(1px 1px at 130px 80px, #00bfff, transparent),
    radial-gradient(2px 2px at 160px 30px, rgba(0, 191, 255, 0.7), transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

/* Content Container */
.parameters-container {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  height: 100vh;
  display: flex;
  align-items: center;
}

.container {
  width: 100%;
}

.parameters-main-layout {
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: 4rem;
  align-items: center;
  min-height: 80vh;
}

/* Left Column - Header and Content */
.parameters-left-column {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

/* Header Section */
.parameters-header {
  text-align: center;
  margin-bottom: 2rem;
}

.section-badge {
  display: inline-block;
  padding: 0.5rem 1.5rem;
  background: linear-gradient(135deg, rgba(0, 191, 255, 0.2), rgba(0, 191, 255, 0.1));
  border: 1px solid rgba(0, 191, 255, 0.3);
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 500;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  color: #00bfff;
  margin-bottom: 1.5rem;
}

.section-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin: 0 0 1.5rem 0;
  background: linear-gradient(135deg, #ffffff, #00bfff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-description {
  font-size: 1.2rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Parameters Content */
.parameters-content {
  flex: 1;
}

.parameter-category {
  display: none;
}

.parameter-category.active {
  display: block;
}

/* Parameter Grid */
.parameter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  max-height: 60vh;
  overflow-y: auto;
  padding-right: 1rem;
}

.parameter-grid::-webkit-scrollbar {
  width: 6px;
}

.parameter-grid::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.parameter-grid::-webkit-scrollbar-thumb {
  background: rgba(0, 191, 255, 0.5);
  border-radius: 3px;
}

.parameter-grid::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 191, 255, 0.7);
}

.parameter-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.parameter-card:hover {
  transform: translateY(-5px);
  border-color: rgba(0, 191, 255, 0.3);
  box-shadow: 0 10px 30px rgba(0, 191, 255, 0.2);
}

.parameter-card.highlight {
  border-color: rgba(0, 191, 255, 0.5);
  background: linear-gradient(135deg, rgba(0, 191, 255, 0.1), rgba(0, 191, 255, 0.05));
}

.param-label {
  font-size: 0.9rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.param-value {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffffff;
  line-height: 1.4;
}

/* Sensor Grid */
.sensor-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.5rem;
  max-height: 60vh;
  overflow-y: auto;
  padding-right: 1rem;
}

.sensor-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.sensor-card:hover {
  transform: translateY(-5px);
  border-color: rgba(0, 191, 255, 0.3);
  box-shadow: 0 10px 30px rgba(0, 191, 255, 0.2);
}

.sensor-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(0, 191, 255, 0.2), rgba(0, 191, 255, 0.1));
  border-radius: 12px;
  flex-shrink: 0;
}

.sensor-icon svg {
  width: 24px;
  height: 24px;
  color: #00bfff;
}

.sensor-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.25rem;
}

.sensor-status {
  font-size: 0.9rem;
  font-weight: 500;
  color: #00ff88;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Right Column - Category Navigation */
.parameters-right-column {
  display: flex;
  align-items: center;
}

.category-nav {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
}

.category-btn {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem 1.5rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  text-align: left;
  width: 100%;
}

.category-btn:hover {
  border-color: rgba(0, 191, 255, 0.3);
  color: #ffffff;
  transform: translateX(5px);
}

.category-btn.active {
  background: linear-gradient(135deg, rgba(0, 191, 255, 0.2), rgba(0, 191, 255, 0.1));
  border-color: rgba(0, 191, 255, 0.5);
  color: #ffffff;
  box-shadow: 0 5px 20px rgba(0, 191, 255, 0.3);
}

.btn-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(0, 191, 255, 0.2), rgba(0, 191, 255, 0.1));
  border-radius: 10px;
  flex-shrink: 0;
}

.btn-icon svg {
  width: 20px;
  height: 20px;
  color: #00bfff;
}

.category-btn.active .btn-icon {
  background: linear-gradient(135deg, #00bfff, #0080ff);
}

.category-btn.active .btn-icon svg {
  color: #ffffff;
}

.btn-text {
  font-weight: 600;
  flex: 1;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .parameters-main-layout {
    grid-template-columns: 1fr 300px;
    gap: 3rem;
  }
  
  .section-title {
    font-size: 3rem;
  }
}

@media (max-width: 992px) {
  .parameters-container {
    padding: 0 1.5rem;
  }
  
  .parameters-main-layout {
    grid-template-columns: 1fr;
    gap: 3rem;
    min-height: auto;
  }
  
  .parameters-section {
    min-height: auto;
    height: auto;
    padding: 4rem 0;
  }
  
  .category-nav {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.75rem;
  }
  
  .category-btn {
    flex: 0 1 auto;
    min-width: 200px;
  }
  
  .parameter-grid,
  .sensor-grid {
    max-height: none;
    overflow-y: visible;
  }
  
  .section-title {
    font-size: 2.5rem;
  }
}

@media (max-width: 768px) {
  .parameters-container {
    padding: 0 1rem;
  }
  
  .parameters-main-layout {
    gap: 2rem;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .section-description {
    font-size: 1.1rem;
  }
  
  .parameter-grid {
    grid-template-columns: 1fr;
  }
  
  .sensor-grid {
    grid-template-columns: 1fr;
  }
  
  .category-nav {
    flex-direction: column;
  }
  
  .category-btn {
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .parameters-section {
    padding: 3rem 0;
  }
  
  .parameters-container {
    padding: 0 0.75rem;
  }
  
  .section-title {
    font-size: 1.75rem;
  }
  
  .parameter-card,
  .sensor-card {
    padding: 1rem;
  }
  
  .category-btn {
    padding: 1rem;
    gap: 0.75rem;
  }
}