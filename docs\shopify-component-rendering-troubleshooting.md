# Shopify 组件渲染问题排查指南

## 核心问题：Git 同步机制

### ⚠️ 最重要的概念
**Shopify CLI 开发服务器只会同步已经被 Git 跟踪的文件！**

这意味着：
- 新创建的文件如果没有 `git add` 就不会被推送到开发环境
- 即使文件在本地存在，开发服务器也看不到它们
- 这是 90% 组件渲染问题的根本原因

## 问题症状
- 组件在 JSON 模板中正确配置
- 本地文件都存在且语法正确
- 开发服务器运行正常
- 但页面上看不到组件

## 完整排查流程

### 第1步：检查 Git 状态 (最关键!)

```bash
git status
```

**重点关注输出中的这些部分：**

```bash
# 如果看到这个，说明文件没有被跟踪！
Untracked files:
  (use "git add <file>..." to include in what will be committed)
	sections/IR3-Parameters-Display.liquid
	assets/IR3-Parameters-Display.css
	assets/IR3-Parameters-Display.js

# 或者这个，说明文件已修改但未提交
Changes not staged for commit:
  (use "git add <file>..." to update what will be committed)
	modified:   templates/page.json
```

### 第2步：理解 Shopify CLI 的同步机制

**Shopify CLI 工作原理：**
1. 启动 `shopify theme dev` 时，CLI 会将 **已跟踪的文件** 推送到开发环境
2. 文件监听器只监控 **已跟踪的文件** 的变化
3. 新文件必须先 `git add` 才会被同步

**验证方法：**
```bash
# 查看哪些文件被 Git 跟踪
git ls-files | grep "你的组件名"

# 如果没有输出，说明文件未被跟踪
```

### 第3步：正确的文件添加流程

#### 3.1 添加所有相关文件
```bash
# 添加主要组件文件
git add sections/Your-Component-Name.liquid

# 添加样式文件
git add assets/Your-Component-Name.css

# 添加脚本文件  
git add assets/Your-Component-Name.js

# 添加页面模板文件（如果是新的）
git add templates/page.your-page.json
```

#### 3.2 验证文件已被暂存
```bash
git status
```

应该看到：
```bash
Changes to be committed:
  (use "git restore --staged <file>..." to unstage)
	new file:   sections/Your-Component-Name.liquid
	new file:   assets/Your-Component-Name.css
	new file:   assets/Your-Component-Name.js
```

#### 3.3 提交文件
```bash
git commit -m "Add Your-Component-Name component

- Added sections/Your-Component-Name.liquid with component structure
- Added assets/Your-Component-Name.css for styling
- Added assets/Your-Component-Name.js for interactivity
- Component configured in page template"
```

### 第4步：强制重启开发服务器

```bash
# 停止当前服务器 (Ctrl+C)
# 等待完全停止，然后重启
shopify theme dev
```

**重要：** 有时需要完全停止服务器并重启，而不是热重载。

### 第5步：验证文件同步

#### 5.1 检查文件是否被推送
在开发服务器启动日志中查找类似信息：
```
Syncing theme files from disk
✓ Uploaded sections/Your-Component-Name.liquid
✓ Uploaded assets/Your-Component-Name.css
✓ Uploaded assets/Your-Component-Name.js
```

#### 5.2 使用浏览器验证
访问你的页面并在控制台执行：
```javascript
// 检查组件是否存在
document.querySelector('.your-component-class')

// 检查 CSS 是否加载
Array.from(document.styleSheets).find(sheet => 
  sheet.href && sheet.href.includes('Your-Component-Name.css')
)

// 检查 JS 是否加载
window.YourComponentClass !== undefined
```

## 高级调试：使用 Playwright 检查

### 自动化检查脚本
```javascript
// 在页面上执行此脚本检查组件状态
const parametersSection = document.querySelector('.parameters-section');
const allSections = document.querySelectorAll('section');

console.log('目标组件存在:', !!parametersSection);
console.log('页面总section数:', allSections.length);

// 列出所有 section 的 ID
allSections.forEach((section, index) => {
  console.log(`Section ${index + 1}:`, {
    id: section.id,
    classes: section.className
  });
});
```

## 常见误区和解决方案

### 误区1: "文件存在就应该工作"
**错误理解：** 以为文件在本地存在就会自动同步  
**正确理解：** 只有 Git 跟踪的文件才会被同步

### 误区2: "重启浏览器能解决问题"
**错误做法：** 清除浏览器缓存、硬刷新  
**正确做法：** 检查 Git 状态，确保文件被跟踪

### 误区3: "配置正确就应该工作"
**错误理解：** JSON 配置正确就足够了  
**正确理解：** 配置 + 文件同步 + 无语法错误 = 正常工作

## 实际案例分析

### 案例：IR3-Parameters-Display 组件不显示

**症状：**
- `page.ir3-v2-show.json` 中正确配置了组件
- `sections/IR3-Parameters-Display.liquid` 文件存在
- 相关 CSS 和 JS 文件存在
- 开发服务器正常运行
- 页面无法显示组件

**排查过程：**
```bash
git status
# 输出显示：
# Untracked files:
#   sections/IR3-Parameters-Display.liquid
#   assets/IR3-Parameters-Display.css
#   assets/IR3-Parameters-Display.js
```

**解决方案：**
```bash
git add sections/IR3-Parameters-Display.liquid
git add assets/IR3-Parameters-Display.css  
git add assets/IR3-Parameters-Display.js
git commit -m "Add IR3-Parameters-Display component"

# 重启开发服务器
shopify theme dev
```

**结果：** 组件立即正常显示

## 预防措施

### 1. 开发工作流程
```bash
# 创建组件时的标准流程
1. 创建 sections/Component.liquid
2. 立即执行: git add sections/Component.liquid
3. 创建 assets/Component.css (如需要)
4. 立即执行: git add assets/Component.css
5. 创建 assets/Component.js (如需要)  
6. 立即执行: git add assets/Component.js
7. 提交: git commit -m "Add Component"
8. 重启开发服务器测试
```

### 2. 定期检查
```bash
# 开发过程中定期执行
git status

# 确保没有未跟踪的关键文件
```

### 3. 团队协作
- 新成员必须了解 Git 同步机制
- 代码审查时检查是否包含所有必要文件
- 使用 `.gitignore` 正确排除不需要的文件

## 总结检查清单

组件不显示时按此顺序检查：

1. [ ] **执行 `git status` 检查文件跟踪状态**
2. [ ] **将所有未跟踪的组件文件添加到 Git**
3. [ ] **提交所有更改**
4. [ ] **完全重启 Shopify 开发服务器**
5. [ ] **验证文件在启动日志中被上传**
6. [ ] **清除浏览器缓存并重新加载页面**
7. [ ] **检查浏览器控制台错误**
8. [ ] **验证 JSON 配置正确性**

**记住：99% 的组件渲染问题都是因为文件没有被 Git 跟踪！**