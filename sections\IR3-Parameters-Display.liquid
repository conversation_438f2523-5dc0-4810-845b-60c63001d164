{% comment %}
  IR3 Parameters Display Section
  File: sections/IR3-Parameters-Display.liquid
{% endcomment %}

{{ 'IR3-Parameters-Display.css' | asset_url | stylesheet_tag }}

<section
  class="parameters-section"
  id="parameters-{{ section.id }}"
  style="margin-top: {{ section.settings.margin_top }}px; margin-bottom: {{ section.settings.margin_bottom }}px;"
>
  <!-- Background Layer -->
  <div class="parameters-background">
    <div class="gradient-overlay"></div>
    <div class="tech-grid"></div>
    <div class="floating-particles"></div>
  </div>

  <!-- Content Container -->
  <div class="parameters-container">
    <div class="container">
      <!-- Header Section - Independent and Centered -->
      <div class="parameters-header">
        <div class="header-content">
          <span class="section-badge">Technical Specifications</span>
          <h2 class="section-title">{{ section.settings.title | default: 'IR3 V2 Parameters' }}</h2>
          <p class="section-description">{{ section.settings.description | default: 'Comprehensive technical specifications and features overview' }}</p>
        </div>
      </div>

      <!-- Category Navigation - Full Width -->
      <div class="parameters-navigation">
        <div class="category-nav">
          <button class="category-btn active" data-category="machine">
            <div class="btn-icon">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5a3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97c0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1c0 .***********.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.**********.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.***********.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z"/>
              </svg>
            </div>
            <span class="btn-text">Machine Parameters</span>
          </button>
          <button class="category-btn" data-category="sensors">
            <div class="btn-icon">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2A10 10 0 0 0 2 12a10 10 0 0 0 10 10a10 10 0 0 0 10-10A10 10 0 0 0 12 2m0 2a8 8 0 0 1 8 8a8 8 0 0 1-8 8a8 8 0 0 1-8-8a8 8 0 0 1 8-8m0 3a5 5 0 0 0-5 5a5 5 0 0 0 5 5a5 5 0 0 0 5-5a5 5 0 0 0-5-5m0 2a3 3 0 0 1 3 3a3 3 0 0 1-3 3a3 3 0 0 1-3-3a3 3 0 0 1 3-3Z"/>
              </svg>
            </div>
            <span class="btn-text">Sensors</span>
          </button>
          <button class="category-btn" data-category="electrical">
            <div class="btn-icon">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M11 21h2v-2h-2v2zm1-18C8.14 3 5.5 5.64 5.5 9c0 2.38 1.19 4.47 3 5.74V17c0 .55.45 1 1 1h5c.55 0 1-.45 1-1v-2.26c1.81-1.27 3-3.36 3-5.74 0-3.36-2.64-6-6-6z"/>
            </div>
            <span class="btn-text">Electrical Hardware</span>
          </button>
          <button class="category-btn" data-category="software">
            <div class="btn-icon">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M4 6h16v2H4V6m0 5h16v2H4v-2m0 5h16v2H4v-2Z"/>
              </svg>
            </div>
            <span class="btn-text">Software</span>
          </button>
        </div>
      </div>

      <!-- Parameters Content - Full Width -->
      <div class="parameters-content">
        <!-- Machine Parameters -->
        <div class="parameter-category active" data-category="machine">
          <div class="parameter-grid">
            <div class="parameter-card">
              <div class="param-label">Printing Technology</div>
              <div class="param-value">FDM</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Machine Structure</div>
              <div class="param-value">Full metal frame</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Motion Structure</div>
              <div class="param-value">CoreXY</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Filament Diameter</div>
              <div class="param-value">1.75mm</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Motor Type</div>
              <div class="param-value">5:1 Dual gear reduction extruder motor</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Nozzle Material</div>
              <div class="param-value">Hardened steel</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Nozzle Size</div>
              <div class="param-value">Standard 0.4mm</div>
            </div>
            <div class="parameter-card highlight">
              <div class="param-label">Print Volume</div>
              <div class="param-value">250×250×∞mm (X*Y*Z)</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Product Dimensions</div>
              <div class="param-value">676×436×510mm</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Package Dimensions</div>
              <div class="param-value">770×510×320mm</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Net Weight</div>
              <div class="param-value">16.5kg</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Gross Weight</div>
              <div class="param-value">21kg</div>
            </div>
            <div class="parameter-card highlight">
              <div class="param-label">Print Precision</div>
              <div class="param-value">±0.1mm</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Layer Thickness</div>
              <div class="param-value">0.1-0.3mm</div>
            </div>
            <div class="parameter-card highlight">
              <div class="param-label">Print Speed</div>
              <div class="param-value">≤400mm/s</div>
            </div>
            <div class="parameter-card highlight">
              <div class="param-label">Print Acceleration</div>
              <div class="param-value">≤20000mm/s²</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Maximum Nozzle Temperature</div>
              <div class="param-value">300°C</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Maximum Heated Bed Temperature</div>
              <div class="param-value">90°C</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Nozzle Heating Time</div>
              <div class="param-value">40s</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Heated Bed Heating Time</div>
              <div class="param-value">90s</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Maximum Flow Rate</div>
              <div class="param-value">26mm³/s</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Print Platform</div>
              <div class="param-value">PEI metal build surface</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Compatible Materials</div>
              <div class="param-value">PLA/PETG/TPU/ABS/ASA, etc.</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Operating Environment Temperature</div>
              <div class="param-value">10-40°C</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Noise Level</div>
              <div class="param-value">54dB</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Print Methods</div>
              <div class="param-value">USB drive/Local network/Internet network</div>
            </div>
          </div>
        </div>

        <!-- Sensors -->
        <div class="parameter-category" data-category="sensors">
          <div class="sensor-grid">
            <div class="sensor-card">
              <div class="sensor-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C10.9 2 10 2.9 10 4s.9 2 2 2s2-.9 2-2s-.9-2-2-2M21 6h-2l-1.5-1.5c-.8-.8-2.1-.8-2.8 0L13.5 6H13c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zM12 20c-1.1 0-2-.9-2-2v-2h2v2h7v-8h-7v2h-2V8h1.5l1.5-1.5l1.5 1.5H19v12c0 1.1-.9 2-2 2H12z"/>
                </svg>
              </div>
              <div class="sensor-name">Vibration Compensation</div>
              <div class="sensor-status supported">Supported</div>
            </div>
            <div class="sensor-card">
              <div class="sensor-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5l1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
              </div>
              <div class="sensor-name">Filament Runout Detection</div>
              <div class="sensor-status supported">Supported</div>
            </div>
            <div class="sensor-card">
              <div class="sensor-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                </svg>
              </div>
              <div class="sensor-name">Material Shortage Detection</div>
              <div class="sensor-status supported">Supported</div>
            </div>
            <div class="sensor-card">
              <div class="sensor-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41c0-1.1-.9-2-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z"/>
                </svg>
              </div>
              <div class="sensor-name">Clogging Detection</div>
              <div class="sensor-status supported">Supported</div>
            </div>
            <div class="sensor-card">
              <div class="sensor-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87L18.18 22L12 18.77L5.82 22L7 14.14l-5-4.87l6.91-1.01L12 2z"/>
                </svg>
              </div>
              <div class="sensor-name">Auto Leveling</div>
              <div class="sensor-status supported">Supported</div>
            </div>
            <div class="sensor-card">
              <div class="sensor-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10s10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3s-3-1.34-3-3s1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22c.03-1.99 4-3.08 6-3.08c1.99 0 5.97 1.09 6 3.08c-1.29 1.94-3.5 3.22-6 3.22z"/>
                </svg>
              </div>
              <div class="sensor-name">LED Lighting</div>
              <div class="sensor-status supported">Supported</div>
            </div>
            <div class="sensor-card">
              <div class="sensor-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4zM14 13h-3v3H9v-3H6v-2h3V8h2v3h3v2z"/>
                </svg>
              </div>
              <div class="sensor-name">Camera</div>
              <div class="sensor-status supported">Supported</div>
            </div>
          </div>
        </div>

        <!-- Electrical Hardware -->
        <div class="parameter-category" data-category="electrical">
          <div class="parameter-grid">
            <div class="parameter-card">
              <div class="param-label">Input Voltage</div>
              <div class="param-value">110VAC/220VAC, 50/60Hz</div>
            </div>
            <div class="parameter-card highlight">
              <div class="param-label">Maximum Power</div>
              <div class="param-value">800W</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Main Controller</div>
              <div class="param-value">64-bit 1.5GHz Quad-core Cortex-A53 processor</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Memory</div>
              <div class="param-value">16GB-SD, 1GB DDR3</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">User Interface</div>
              <div class="param-value">4.3-inch touchscreen with 800×480 resolution</div>
            </div>
            <div class="parameter-card highlight">
              <div class="param-label">Printing Firmware</div>
              <div class="param-value">Klipper</div>
            </div>
          </div>
        </div>

        <!-- Software -->
        <div class="parameter-category" data-category="software">
          <div class="parameter-grid">
            <div class="parameter-card">
              <div class="param-label">Slicing Software</div>
              <div class="param-value">Ideamaker/Ideaformer Cura</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Input Formats</div>
              <div class="param-value">.stl/.obj/.3mf/.step/.stp/.iges/.igs/.oltp/.jpg/.jpeg/.png/.bmp</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Operating Systems</div>
              <div class="param-value">Windows/MacOS/Linux</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Output File Format</div>
              <div class="param-value">.gcode</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

{{ 'IR3-Parameters-Display.js' | asset_url | script_tag }}

{% schema %}
{
  "name": "IR3 Parameters Display",
  "tag": "section",
  "class": "parameters-display-section",
  "settings": [
    {
      "type": "header",
      "content": "Content Settings"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Section Title",
      "default": "IR3 V2 Parameters"
    },
    {
      "type": "textarea",
      "id": "description",
      "label": "Section Description",
      "default": "Comprehensive technical specifications and features overview"
    },
    {
      "type": "header",
      "content": "Spacing Settings"
    },
    {
      "type": "number",
      "id": "margin_top",
      "label": "Top Margin (px)",
      "default": 0,
      "info": "输入精确的上边距像素值"
    },
    {
      "type": "number",
      "id": "margin_bottom",
      "label": "Bottom Margin (px)",
      "default": 0,
      "info": "输入精确的下边距像素值"
    }
  ],
  "presets": [
    {
      "name": "IR3 Parameters Display"
    }
  ]
}
{% endschema %}